"use client";

import Footer from "@/components/Footer";
import { GetInTouchModal } from "@/components/GetInTouchModal";
import BookADemo from "@/components/Home/BookADemo";
import { Complaints } from "@/components/Home/Complaints";
import { Features } from "@/components/Home/Features/Features";
import { GetInTouch } from "@/components/Home/GetInTouch";
import HeroSection from "@/components/Home/HeroSection";
import { Interactions } from "@/components/Home/Interactions";
import { Difference } from "@/components/Home/MakesUsDifferent/Difference";
import Navbar from "@/components/Home/Navbar";
import { Partners } from "@/components/Home/Partners";
import { Results } from "@/components/Home/Results";
import { Testimonial } from "@/components/Home/Testimonial";
import { Testing } from "@/components/Home/Testing";
import { NextPage } from "next";
import { useState } from "react";

const Home: NextPage = () => {
	const [showBookADemo, setShowBookDemo] = useState(false);
	const [showContactUsModal, setshowContactUsModal] = useState(false);
	return (
		<>
			<main className="">
				<Navbar
					showBookADemo={showBookADemo}
					setShowBookDemo={setShowBookDemo}
					showContactUsModal={showContactUsModal}
					setshowContactUsModal={setshowContactUsModal}
				/>
				<HeroSection setShowBookDemo={setShowBookDemo} />
				<Partners />
				<Features />
				{/* <Testing /> */}
				<Interactions />
				<Difference />
				<Complaints />
				<Results />
				<Testimonial />
				<GetInTouch setshowContactUsModal={setshowContactUsModal} />
				<Footer setshowContactUsModal={setshowContactUsModal} />
				{/* <CoreFeatures /> */}
				{/* <HoursSaved />
				<HowItWorks setShowBookDemo={setShowBookDemo} />
				<Subscribe />
				<WhatUsersSay />
				<OptimiseOperations
					setShowBookDemo={setShowBookDemo}
					setshowContactUsModal={setshowContactUsModal}
				/>
				<Footer /> */}
			</main>
			<GetInTouchModal
				show={showContactUsModal}
				setShow={setshowContactUsModal}
			/>
			<BookADemo
				showBookADemo={showBookADemo}
				setShowBookDemo={setShowBookDemo}
			/>
			{/* <ContactUsModal
				show={showContactUsModal}
				setShow={setshowContactUsModal}
			/>
			<BookADemo
				showBookADemo={showBookADemo}
				setShowBookDemo={setShowBookDemo}
			/> */}
		</>
	);
};

export default Home;
