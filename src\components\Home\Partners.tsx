import React, { useRef, useState, useEffect } from "react";

export const Partners = () => {
	const partnerLogos = [
		{ src: "/assets/images/OH-logo.svg", alt: "Ontario Health" },
		{
			src: "/assets/images/UOW.svg",
			alt: "University of Waterloo",
		},
		{
			src: "/assets/images/NM.svg",
			alt: "Network Medicals",
		},
		{
			src: "/assets/images/NWT.svg",
			alt: "North Western Toronto",
		},
		{ src: "/assets/images/microsoft.svg", alt: "Microsoft" },
		{ src: "/assets/images/velocity.png", alt: "Velocity" },
	];

	// State and refs for mobile carousel
	const carouselRef = useRef<HTMLDivElement>(null);
	const [isDragging, setIsDragging] = useState(false);
	const [startX, setStartX] = useState(0);
	const [scrollLeft, setScrollLeft] = useState(0);
	const [isAutoScrollPaused, setIsAutoScrollPaused] = useState(false);

	// Handle touch/mouse start
	const handleStart = (clientX: number) => {
		if (!carouselRef.current) return;
		setIsDragging(true);
		setIsAutoScrollPaused(true);
		setStartX(clientX - carouselRef.current.offsetLeft);
		setScrollLeft(carouselRef.current.scrollLeft);
	};

	// Handle touch/mouse move
	const handleMove = (clientX: number) => {
		if (!isDragging || !carouselRef.current) return;
		const x = clientX - carouselRef.current.offsetLeft;
		const walk = (x - startX) * 2; // Multiply by 2 for faster scrolling
		carouselRef.current.scrollLeft = scrollLeft - walk;
	};

	// Handle touch/mouse end
	const handleEnd = () => {
		setIsDragging(false);
		// Resume auto-scroll after a delay
		setTimeout(() => {
			setIsAutoScrollPaused(false);
		}, 3000); // Resume after 3 seconds of inactivity
	};

	// Mouse events
	const handleMouseDown = (e: React.MouseEvent) => {
		e.preventDefault();
		handleStart(e.pageX);
	};

	const handleMouseMove = (e: React.MouseEvent) => {
		e.preventDefault();
		handleMove(e.pageX);
	};

	// Touch events
	const handleTouchStart = (e: React.TouchEvent) => {
		handleStart(e.touches[0].clientX);
	};

	const handleTouchMove = (e: React.TouchEvent) => {
		handleMove(e.touches[0].clientX);
	};

	// Auto-scroll effect
	useEffect(() => {
		if (!carouselRef.current || isAutoScrollPaused) return;

		const carousel = carouselRef.current;
		const scrollWidth = carousel.scrollWidth;
		const clientWidth = carousel.clientWidth;
		const maxScroll = scrollWidth - clientWidth;

		let animationId: number;
		let startTime: number;
		const duration = 38000; // 38 seconds to match original animation

		const animate = (currentTime: number) => {
			if (!startTime) startTime = currentTime;
			const elapsed = currentTime - startTime;
			const progress = (elapsed % duration) / duration;

			if (carousel) {
				carousel.scrollLeft = progress * maxScroll;
			}

			if (!isAutoScrollPaused) {
				animationId = requestAnimationFrame(animate);
			}
		};

		animationId = requestAnimationFrame(animate);

		return () => {
			if (animationId) {
				cancelAnimationFrame(animationId);
			}
		};
	}, [isAutoScrollPaused]);

	return (
		<section className="w-full py-[52px] sm:mt-[30px] lg:py-12 xl:mt-10">
			<div className="hidden lg:flex lg:items-center lg:justify-center">
				<div className="flex flex-wrap items-center justify-center gap-[52.96px]">
					{partnerLogos.map((logo, idx) => (
						<img
							key={idx}
							src={logo.src}
							alt={logo.alt}
							className="h-8 w-auto flex-shrink-0 grayscale transition-all duration-300 hover:grayscale-0"
							style={{ maxWidth: 140 }}
						/>
					))}
				</div>
			</div>

			{/*Mobile*/}
			<div className="overflow-hidden lg:hidden">
				<div
					ref={carouselRef}
					className="scrollbar-hide flex cursor-grab gap-[52.96px] overflow-x-auto active:cursor-grabbing"
					style={{
						width: "100%",
						scrollBehavior: isDragging ? "auto" : "smooth",
					}}
					onMouseDown={handleMouseDown}
					onMouseMove={handleMouseMove}
					onMouseUp={handleEnd}
					onMouseLeave={handleEnd}
					onTouchStart={handleTouchStart}
					onTouchMove={handleTouchMove}
					onTouchEnd={handleEnd}
				>
					<div
						className="flex gap-[52.96px]"
						style={{ width: "max-content" }}
					>
						{[
							...partnerLogos,
							...partnerLogos,
							...partnerLogos,
						].map((logo, idx) => (
							<div key={idx} className="flex-shrink-0">
								<img
									src={logo.src}
									alt={logo.alt}
									className="pointer-events-none h-8 w-auto select-none grayscale"
									style={{ maxWidth: 140 }}
									draggable={false}
								/>
							</div>
						))}
					</div>
				</div>
			</div>

			<style>{`
				.scrollbar-hide {
					-ms-overflow-style: none;
					scrollbar-width: none;
				}
				.scrollbar-hide::-webkit-scrollbar {
					display: none;
				}
			`}</style>
		</section>
	);
};
